import os
from openpyxl import load_workbook

def is_valid_worksheet(ws):
    """检查工作表是否有效"""
    try:
        _ = ws.title
        _ = ws.max_row
        _ = ws.max_column
        return True
    except:
        return False

def set_print_settings_and_remove_sheets(xlsx_path):
    """设置打印属性并删除多余的工作表"""
    try:
        print(f"正在打开文件: {os.path.basename(xlsx_path)}")
        wb = load_workbook(xlsx_path)
        
        # 检查工作表数量
        if len(wb.sheetnames) > 0:
            # 保存第一个工作表名称
            first_sheet_name = wb.sheetnames[0]
            
            # 删除其他工作表
            if len(wb.sheetnames) > 1:
                print(f"删除其他工作表（保留 {first_sheet_name}）:")
                for sheet_name in wb.sheetnames[1:]:
                    print(f"- 删除工作表: {sheet_name}")
                    wb.remove(wb[sheet_name])
            
            # 处理第一个工作表
            try:
                ws = wb[first_sheet_name]
                print(f"正在处理第一个工作表: {first_sheet_name}")
                
                # 检查工作表是否有效
                if not is_valid_worksheet(ws):
                    print(f"警告: 工作表 {first_sheet_name} 无效，跳过")
                    return False
                
                try:
                    # 设置页面边距
                    
                    
                    # 设置打印区域和选项
                    ws.print_options.horizontalCentered = True
                    ws.print_area = None  # 清除任何现有的打印区域
                    
                    # 设置页面布局
                   
                    ws.page_setup.paperSize = 9        # 9 代表 A4 纸张
                    ws.page_setup.orientation = 'portrait'  # 纵向打印
                    ws.page_setup.fitToPage = True
                    ws.page_setup.fitToHeight = 0  # 0表示自动
                    ws.page_setup.fitToWidth = 1   # 确保所有列在一页内
                    
                    print(f"成功设置工作表 {first_sheet_name} 的打印属性")
                    
                except Exception as setup_error:
                    print(f"设置打印属性时出错: {str(setup_error)}")
                    # 即使设置某些属性失败，也继续处理
                
            except Exception as e:
                print(f"处理工作表 {first_sheet_name} 时出错: {str(e)}")
                return False
        else:
            print("文件中没有工作表")
            return False
        
        print(f"正在保存文件: {os.path.basename(xlsx_path)}")
        # 尝试以不同的方式保存文件
        try:
            wb.save(xlsx_path)
        except Exception as save_error:
            print(f"常规保存失败，尝试另存为新文件: {str(save_error)}")
            new_path = os.path.splitext(xlsx_path)[0] + "_new.xlsx"
            wb.save(new_path)
            if os.path.exists(new_path):
                os.replace(new_path, xlsx_path)
        
        print(f"成功保存文件: {os.path.basename(xlsx_path)}")
        return True
        
    except Exception as e:
        print(f"处理文件失败 {os.path.basename(xlsx_path)}: {str(e)}")
        return False
    finally:
        try:
            wb.close()
        except:
            pass

def main():
    directory = "/home/<USER>/下载/外审表01/外审表"
    
    # 遍历目录中的所有 xlsx 文件
    total_files = 0
    success_files = 0
    failed_files = []
    
    for filename in os.listdir(directory):
        if filename.endswith('.xlsx'):
            total_files += 1
            xlsx_path = os.path.join(directory, filename)
            print(f"\n{'='*50}")
            print(f"处理文件 ({total_files}): {filename}")
            
            if set_print_settings_and_remove_sheets(xlsx_path):
                success_files += 1
            else:
                failed_files.append(filename)
    
    print(f"\n处理完成！")
    print(f"总文件数: {total_files}")
    print(f"成功处理: {success_files}")
    print(f"失败数量: {total_files - success_files}")
    
    if failed_files:
        print("\n处理失败的文件:")
        for failed_file in failed_files:
            print(f"- {failed_file}")

if __name__ == "__main__":
    main()
