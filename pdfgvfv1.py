import pymupdf as fitz
from PIL import Image
import os
import io

def add_transparent_stamp(input_pdf, output_pdf, stamp_image):
    # 打开 PDF 和印章图像
    pdf_document = fitz.open(input_pdf)
    
    # 使用 Pillow 打开并处理图像
    stamp = Image.open(stamp_image)
    
    # 优化透明度处理
    # 1. 转换为 RGBA 模式
    stamp = stamp.convert("RGBA")
    
    # 2. 创建一个新的透明背景图像
    new_stamp = Image.new('RGBA', stamp.size, (255, 255, 255, 0))
    
    # 3. 根据原图的 alpha 通道创建遮罩
    for x in range(stamp.width):
        for y in range(stamp.height):
            r, g, b, a = stamp.getpixel((x, y))
            # 降低不透明度，增加透明效果
            if a > 0:
                new_stamp.putpixel((x, y), (r, g, b, int(a * 1.0)))  # 将透明度降低到原来的100%
    
    # 将处理后的图像转换为字节流
    img_byte_arr = io.BytesIO()
    new_stamp.save(img_byte_arr, format='PNG')
    img_byte_arr = img_byte_arr.getvalue()
    
    # 使用字节流创建 Pixmap
    stamp_pixmap = fitz.Pixmap(img_byte_arr)
    
    # 创建一个新的 PDF 来保存加盖印章的页面
    output_doc = fitz.open()
    
    # 处理每一页
    for page_num in range(len(pdf_document)):
        # 获取当前页
        page = pdf_document[page_num]
        
        # 创建页面的副本
        new_page = output_doc.new_page(width=page.rect.width, height=page.rect.height)
        
        # 将原始页面内容复制到新页面
        new_page.set_cropbox(page.rect)
        new_page.show_pdf_page(page.rect, pdf_document, page_num)
        
        # 计算印章位置（左上角）
        stamp_width = stamp_pixmap.width
        stamp_height = stamp_pixmap.height
        
        # 在左上角添加印章，留出 50 像素的边距
        stamp_x = 50  # 距离左边 50 像素
        stamp_y = 50  # 距离顶部 50 像素
        
        # 创建矩形区域用于插入图像
        stamp_rect = fitz.Rect(stamp_x, stamp_y, stamp_x + stamp_width, stamp_y + stamp_height)
        
        # 添加透明印章
        new_page.insert_image(stamp_rect, pixmap=stamp_pixmap)
    
    # 保存带有印章的 PDF
    output_doc.save(output_pdf)
    
    # 关闭文档
    pdf_document.close()
    output_doc.close()

# 确保 static 目录存在
static_dir = "static/"
os.makedirs(static_dir, exist_ok=True)

# 路径
input_pdf = "/home/<USER>/下载/外审表01/外审表/merged_output.pdf"
output_pdf = "/home/<USER>/下载/外审表01/外审表/stamped_output.pdf"
stamp_image = "/home/<USER>/下载/外审表01/g2.png"

# 添加印章到 PDF
add_transparent_stamp(input_pdf, output_pdf, stamp_image)
print(f"已将盖章 PDF 保存到 {output_pdf}")
