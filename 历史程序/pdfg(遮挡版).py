import pymupdf as fitz
from PIL import Image
import os
import io
import numpy as np
import cv2

def remove_background(image, debug=False):
    # 将图像转换为 numpy 数组
    img_array = np.array(image)
    
    # 转换为灰度图像
    gray = cv2.cvtColor(img_array, cv2.COLOR_RGBA2GRAY)
    
    # 使用更温和的高斯模糊
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)
    
    # <PERSON><PERSON>'s 二值化方法 - 自动确定最佳阈值
    _, binary = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    
    # 形态学处理：更温和的去噪
    kernel = np.ones((2,2), np.uint8)
    cleaned = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel, iterations=1)
    
    # 创建 alpha 通道
    alpha = np.zeros(img_array.shape[:2], dtype=np.uint8)
    alpha[cleaned == 255] = 255
    
    # 如果去除背景后图像太空，则回退到原始图像
    if np.sum(alpha) / alpha.size < 0.1:  # 如果保留的像素少于10%
        alpha = np.ones(img_array.shape[:2], dtype=np.uint8) * 255
    
    # 创建新的 RGBA 图像
    result = np.zeros((*img_array.shape[:2], 4), dtype=np.uint8)
    
    # 使用原始图像的颜色，但应用新的 alpha 通道
    result[:, :, :3] = img_array[:, :, :3]
    result[:, :, 3] = alpha
    
    # 调试：保存中间处理结果
    if debug:
        debug_img = Image.fromarray(result)
        debug_img.save('/tmp/debug_stamp.png')
        
        # 额外保存中间处理图像以便诊断
        cv2.imwrite('/tmp/debug_gray.png', gray)
        cv2.imwrite('/tmp/debug_blurred.png', blurred)
        cv2.imwrite('/tmp/debug_binary.png', binary)
        cv2.imwrite('/tmp/debug_cleaned.png', cleaned)
    
    return Image.fromarray(result)

def add_transparent_stamp(input_pdf, output_pdf, stamp_image):
    # 打开 PDF 和印章图像
    pdf_document = fitz.open(input_pdf)
    
    # 使用 Pillow 打开并处理图像
    stamp = Image.open(stamp_image)
    
    # 转换为 RGBA 模式
    stamp = stamp.convert("RGBA")
    
    # 移除背景，启用调试模式
    new_stamp = remove_background(stamp, debug=True)
    
    # 将处理后的图像转换为字节流
    img_byte_arr = io.BytesIO()
    new_stamp.save(img_byte_arr, format='PNG')
    img_byte_arr = img_byte_arr.getvalue()
    
    # 使用字节流创建 Pixmap
    stamp_pixmap = fitz.Pixmap(img_byte_arr)
    
    # 创建一个新的 PDF 来保存加盖印章的页面
    output_doc = fitz.open()
    
    # 处理每一页
    for page_num in range(len(pdf_document)):
        # 获取当前页
        page = pdf_document[page_num]
        
        # 创建页面的副本
        new_page = output_doc.new_page(width=page.rect.width, height=page.rect.height)
        
        # 将原始页面内容复制到新页面
        new_page.set_cropbox(page.rect)
        new_page.show_pdf_page(page.rect, pdf_document, page_num)
        
        # 计算印章位置（左上角）
        stamp_width = stamp_pixmap.width
        stamp_height = stamp_pixmap.height
        
        # 在左上角添加印章，留出 50 像素的边距
        stamp_x = 50  # 距离左边 50 像素
        stamp_y = 50  # 距离顶部 50 像素
        
        # 创建矩形区域用于插入图像
        stamp_rect = fitz.Rect(stamp_x, stamp_y, stamp_x + stamp_width, stamp_y + stamp_height)
        
        # 添加印章
        new_page.insert_image(stamp_rect, pixmap=stamp_pixmap)
    
    # 保存带有印章的 PDF
    output_doc.save(output_pdf)
    
    # 关闭文档
    pdf_document.close()
    output_doc.close()

# 确保 static 目录存在
static_dir = "static/"
os.makedirs(static_dir, exist_ok=True)

# 路径
input_pdf = "/home/<USER>/下载/外审表01/外审表/merged_output.pdf"
output_pdf = "/home/<USER>/下载/外审表01/外审表/stamped_output.pdf"
stamp_image = "/home/<USER>/下载/外审表01/g.JPG"

# 添加印章到 PDF
add_transparent_stamp(input_pdf, output_pdf, stamp_image)
print(f"已将盖章 PDF 保存到 {output_pdf}")